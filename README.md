# Bambu Labs Printer Control

Este proyecto permite controlar una impresora Bambu Labs usando Python.

## Configuración del entorno de desarrollo

### Prerrequisitos
- Python 3.7 o superior
- pip (gestor de paquetes de Python)

### Instalación

1. **Crear entorno virtual:**
   ```bash
   python -m venv venv
   ```

2. **Activar el entorno virtual:**
   
   En Windows:
   ```bash
   venv\Scripts\activate
   ```
   
   En Linux/macOS:
   ```bash
   source venv/bin/activate
   ```

3. **Instalar dependencias:**
   ```bash
   pip install -r requirements.txt
   ```

### Configuración

Antes de ejecutar el script, asegúrate de actualizar los siguientes valores en `main.py`:

- `IP`: La dirección IP de tu impresora Bambu Labs
- `ACCESS_CODE`: El código de acceso de tu impresora
- `SERIAL`: El número de serie de tu impresora

### Ejecución

Para ejecutar el script principal:

```bash
python main.py
```

### Dependencias

- `bambulabs-api`: Biblioteca para interactuar con impresoras Bambu Labs
- `paho-mqtt`: Cliente MQTT (dependencia de bambulabs-api)
- `pillow`: Biblioteca de procesamiento de imágenes (dependencia de bambulabs-api)

## Notas

- Asegúrate de que tu impresora esté conectada a la misma red que tu computadora
- Verifica que el código de acceso y número de serie sean correctos
- La impresora debe estar encendida y accesible por red
